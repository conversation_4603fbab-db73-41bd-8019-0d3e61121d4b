import { useState, useCallback } from 'react'
import { Message } from '@/components/common/FloatingMessages'
import { v4 as uuidv4 } from 'uuid'

export const useMessages = () => {
  const [messages, setMessages] = useState<Message[]>([])

  const addMessage = useCallback((
    text: string, 
    type: 'success' | 'error' | 'info' | 'warning' = 'info',
    duration = 5000
  ) => {
    const id = uuidv4()
    setMessages(prev => [...prev, { id, text, type, duration }])
    return id
  }, [])

  const removeMessage = useCallback((id: string) => {
    setMessages(prev => prev.filter(message => message.id !== id))
  }, [])

  return {
    messages,
    addMessage,
    removeMessage
  }
}