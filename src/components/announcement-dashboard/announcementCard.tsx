// announcementCard.tsx
import React from "react";
import { <PERSON><PERSON><PERSON>, Trash2, Pin } from "lucide-react";
import clsx from "clsx";

type Announcement = {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  type: {
    id: number;
    typeName: string;
  };
  category: {
    id: number;
    categoryName: string;
  };
  isPinned: boolean;
  isActive: boolean;
};

interface Props {
  data: Announcement;
  onDelete: (id: number) => void;
}

const AnnouncementCard: React.FC<Props> = ({ data, onDelete }) => {
  const handleDeleteClick = () => {
    console.log("AnnouncementCard data:", data?.id);
    if (window.confirm("Are you sure you want to delete this announcement?")) {
      onDelete(data?.id);
    }
  };

  return (
    <div className="bg-gradient-to-br from-gray-200 via-gray-300 to-gray-400 rounded-2xl px-6 py-5 shadow-lg mb-6 mx-6 mt-10 transform transition-all duration-300 hover:scale-105 hover:shadow-xl cursor-pointer group">
      <div className="grid grid-cols-3 items-center gap-4">
        {/* Left - ID + Pin */}
        <div className="flex flex-col items-start space-y-2">
          <span className="text-xs text-gray-700 font-semibold">
            ID: {data.id}
          </span>
          {data.isPinned && (
            <div className="animate-bounce text-yellow-600" title="Pinned">
              <Pin size={22} />
            </div>
          )}
        </div>

        {/* Center - Title + Dates */}
        <div className="text-center">
          <h2 className="text-xl font-extrabold text-gray-800 group-hover:text-indigo-700 transition duration-300">
            {data.title}
          </h2>
          <div className="flex justify-center gap-8 mt-2 text-sm text-gray-700">
            <div className="text-left">
              <p className="font-semibold">From:</p>
              <p>{new Date(data.startDate).toLocaleDateString()}</p>
              <p className="text-sm italic text-indigo-600">
                {data.type?.typeName}
              </p>
            </div>
            <div className="text-left">
              <p className="font-semibold">To:</p>
              <p>{new Date(data.endDate).toLocaleDateString()}</p>
              <p className="text-sm italic text-indigo-600">
                {data.category?.categoryName}
              </p>
            </div>
          </div>
        </div>

        {/* Right - Status + Icons */}
        <div className="flex flex-col items-end space-y-3">
          <p
            className={clsx(
              "text-sm font-semibold",
              data.isActive ? "text-green-600" : "text-red-500"
            )}
          >
            {data.isActive ? "Active" : "Inactive"}
          </p>
          <div className="flex gap-3">
            <div
              className="hover:bg-indigo-100 p-1 rounded-full transition"
              title="Edit"
            >
              <Pencil className="cursor-pointer text-indigo-700" size={18} />
            </div>
            <div
              className="hover:bg-red-100 p-1 rounded-full transition"
              title="Delete"
              onClick={handleDeleteClick}
            >
              <Trash2 className="cursor-pointer text-red-600" size={18} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementCard;
