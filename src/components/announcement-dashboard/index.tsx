import React, { useEffect } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";

const Index: React.FC = () => {
  const [selectedType, setSelectedType] = React.useState<string>("");
  const [selectedCategory, setSelectedCategory] = React.useState<string>("");
  const [selectedStatus, setSelectedStatus] = React.useState<string>("");
  const [startDate, setStartDate] = React.useState<Date | null>(null);
  const [endDate, setEndDate] = React.useState<Date | null>(null);

  const {
    orgAnnouncement,
    annoucementType,
    allAnnouncements,
    deleteAnnouncement,
  } = useSelector((state: RootState) => state.announcementDashboard);
  const dispatch = useDispatch<AppDispatch>();

  const handleAnnouncementTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedType(e.target.value);
  };

  const handleCategoryTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedCategory(e.target.value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatus(e.target.value);
  };

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setStartDate(dates[0]);
    setEndDate(dates[1]);
  };

  const statusOptions = [
    {
      label: "Publish",
      value: "Publish",
    },
    {
      label: "Unpublish",
      value: "Unpublish",
    },
  ];

  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllAnnouncements());
  }, []);

  const handleDelete = async (id: number) => {
    await dispatch(deleteAnnouncement({ id }));
    dispatch(fetchAllAnnouncements());
  };

  // console.log("annoucementTypeannoucementTypeannoucementType", annoucementType);
  console.log("hello");

  return (
    <div className="min-h-screen bg-white py-14 px-4 md:px-24">
      <div className="w-full max-w-6xl mx-auto p-0 bg-white rounded-md">
        <form className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-12 items-end bg-gradient-to-br from-gray-200 via-gray-300 to-gray-400 rounded-2xl p-10 shadow-xl mx-auto mt-4 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.01]">
          {/* Announcement Type */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Announcement Type
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <CustomDropdown
                options={annoucementType?.data?.map((item) => item?.typeName)}
                selectedValue={selectedType}
                handleChange={handleAnnouncementTypeChange}
              />
            </div>
          </div>

          {/* Date Range */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Date Range
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onDateChange={handleDateChange}
              />
            </div>
          </div>

          {/* Announcement Category */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Announcement Category
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <CustomDropdown
                options={orgAnnouncement?.data?.map(
                  (item) => item?.categoryName
                )}
                selectedValue={selectedCategory}
                handleChange={handleCategoryTypeChange}
              />
            </div>
          </div>

          {/* Status + Submit Button */}
          <div className="flex items-end gap-4">
            <div className="flex-1">
              <label className="text-sm font-semibold text-gray-800 mb-1">
                Status
              </label>
              <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
                <CustomDropdown
                  options={statusOptions?.map((item) => item?.value)}
                  selectedValue={selectedStatus}
                  handleChange={handleStatusChange}
                />
              </div>
            </div>
            <Button className="bg-indigo-600 hover:bg-indigo-700 text-white transition duration-300 shadow-md hover:shadow-lg transform hover:scale-105 px-5 py-2.5 rounded-md">
              <span className="text-sm font-semibold">Submit</span>
            </Button>
          </div>
        </form>
      </div>

      {allAnnouncements?.data?.map((item) => {
        console.log("itemcheck", item);
        return (
          <AnnouncementCard key={item.id} data={item} onDelete={handleDelete} />
        );
      })}
    </div>
  );
};

export default Index;
