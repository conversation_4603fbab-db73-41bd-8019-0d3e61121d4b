'use client'

import { X } from "lucide-react"
import { useEffect, useState } from "react"
import { AnimatePresence, motion } from "framer-motion"

export interface Message {
  id: string
  text: string
  type: 'success' | 'error' | 'info' | 'warning'
  duration?: number
}

interface FloatingMessagesProps {
  messages: Message[]
  onDismiss: (id: string) => void
}

const FloatingMessages: React.FC<FloatingMessagesProps> = ({ 
  messages, 
  onDismiss 
}) => {
  return (
    <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-2">
      <AnimatePresence>
        {messages.map((message) => (
          <MessageItem 
            key={message.id} 
            message={message} 
            onDismiss={onDismiss} 
          />
        ))}
      </AnimatePresence>
    </div>
  )
}

const MessageItem = ({ message, onDismiss }: { 
  message: Message, 
  onDismiss: (id: string) => void 
}) => {
  const [isVisible, setIsVisible] = useState(true)
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(() => onDismiss(message.id), 300)
    }, message.duration || 5000)
    
    return () => clearTimeout(timer)
  }, [message, onDismiss])
  
  const getBgColor = () => {
    switch(message.type) {
      case 'success': return 'bg-green-100 border-green-500'
      case 'error': return 'bg-red-100 border-red-500'
      case 'warning': return 'bg-yellow-100 border-yellow-500'
      default: return 'bg-blue-100 border-blue-500'
    }
  }
  
  const getTextColor = () => {
    switch(message.type) {
      case 'success': return 'text-green-700'
      case 'error': return 'text-red-700'
      case 'warning': return 'text-yellow-700'
      default: return 'text-blue-700'
    }
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20, scale: isVisible ? 1 : 0.5 }}
      exit={{ opacity: 0, scale: 0.5, y: 20 }}
      transition={{ duration: 0.3 }}
      className={`${getBgColor()} ${getTextColor()} p-4 rounded-lg shadow-lg border-l-4 min-w-[300px] max-w-md`}
    >
      <div className="flex justify-between items-start">
        <p className="font-medium">{message.text}</p>
        <button 
          onClick={() => {
            setIsVisible(false)
            setTimeout(() => onDismiss(message.id), 300)
          }}
          className="ml-2 hover:bg-gray-200 p-1 rounded-full"
        >
          <X size={16} />
        </button>
      </div>
    </motion.div>
  )
}

export default FloatingMessages