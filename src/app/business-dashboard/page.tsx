"use client";
import SportsBusiness from "../../components/announcement-dashboard/index";
//import FloatingMessagesWidget from "@/components/messages/FloatingMessagesWidget";

import { useRoleBasedRoute } from "@/hooks/useRoleBasedRouting";

const AthleteDashboard = () => {
  const { isAuthenticated } = useRoleBasedRoute();

  if (!isAuthenticated) return;
  return (
    <>
      <SportsBusiness />
      {/* <FloatingMessagesWidget /> */}
    </>
  );
};
export default AthleteDashboard;
