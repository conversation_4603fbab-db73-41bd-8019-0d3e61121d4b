"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useEffect, useRef } from "react";

interface AnnouncementItem {
  id: number;
  title: string;
  sport: string;
  date: string;
}

interface SpotLightsProps {
  data: AnnouncementItem[];
}

const SpotLights = ({ data }: SpotLightsProps) => {
  const carouselRef = useRef<any>(null);

  const formatToDayMonthYear = (isoDate: string): string => {
    const date = new Date(isoDate);
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (carouselRef.current && data.length > 0) {
        carouselRef.current.scrollTo(
          (carouselRef.current.selectedScrollSnap() + 1) % data.length
        );
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [data]);

  if (!data.length) return null;

  return (
    <div className="bg-slate-100 flex flex-col p-4 rounded-md">
      <h2 className="font-bold text-xl mx-auto pb-2">Spotlight</h2>
      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        setApi={(api) => (carouselRef.current = api)}
        orientation="vertical"
        className="w-full max-w-xs mx-auto h-full"
      >
        <CarouselContent className="h-[450px] w-full gap-1">
          {data?.map((item: any) => {
            console.log("itemitem", item);
            return (
              <CarouselItem key={item.id} className="basis-1/3">
                <Card>
                  <CardHeader>
                    <CardTitle className="font-bold">{item.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex justify-between flex-wrap gap-3">
                    <span className="font-semibold text-md">
                      {item?.type?.typeName}
                    </span>
                    <span className="text-secondary text-md font-semibold">
                      {item?.category?.categoryName}
                    </span>

                    <div className="gap-x-5">
                      <span className="text-secondary text-md font-semibold">
                        {formatToDayMonthYear(item?.startDate)}
                      </span>
                      <span className="text-secondary text-md font-semibold">
                        {" - " + formatToDayMonthYear(item?.endDate)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default SpotLights;
