"use client";
import ClientGuard from "@/components/ClientGuard";
import CustomDropdown from "@/components/common/DropDown";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllLocations,
  fetchAllSpecialities,
  fetchAllSports,
  fetchAllStates,
} from "@/store/slices/commonSlice";
import { fetchAllAnnouncements } from "@/store/slices/org-announcement/announcement";
import { fetchOrgProfile } from "@/store/slices/org-announcement/sportsBusiness";
import { Globe, Mail, PencilLine, Phone } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { FaInstagram, FaXTwitter, FaYoutube } from "react-icons/fa6";
import { useDispatch, useSelector } from "react-redux";
import Select from "react-select";
import SpotLights from "./spotlight";

const BusinessProfile = () => {
  const [orgName, setOrgName] = useState("Organization Name");
  const [editingName, setEditingName] = useState(false);
  const [blurb, setBlurb] = useState(
    "We are a dynamic organization offering hybrid solutions in tech, consulting, and innovation. Join us to revolutionize the digital world."
  );
  const [about, setAbout] = useState(
    "This section can include more details about the organization..."
  );
  const [serviceChannel, setServiceChannel] = useState();
  const [contact, setContact] = useState({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [businessName, setBusinessName] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(false);
  // orgProfile?.[0]?.user?.isEmailVerified ?? false
  const [resendCooldown, setResendCooldown] = useState<number>(0);
  const [resendMessage, setResendMessage] = useState<string>("");

  const { orgProfile, selectedState, selectedLocations, selectedSport, selectedSpecialities } = useSelector((state: RootState) => state.orgProfile);
  const { allAnnouncements, } = useSelector(
    (state: RootState) => state.announcementDashboard
  );

  const {
    allStatesList,
    allLocationsList,
    allSportsList,
    allSpecilitiesList,
  } = useSelector((state: RootState) => state.commonSlice);

  const dispatch = useDispatch<AppDispatch>();

  const serviceChannelOptions: any = [
    { label: "In Person", value: "In Person" },
    { label: "Online", value: "Online" },
    { label: "Hybrid", value: "Hybrid" },
  ];

  const formatToDayMonthYear = (isoDate: string): string => {
    const date = new Date(isoDate);
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const handleResendVerification = () => {
    // Simulate API call
    setResendMessage("Verification email sent!");
    setResendCooldown(30); // 30 seconds cooldown
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleChange = (e?: any) => {
    setServiceChannel(e.target.value);
  };

  const socialMediaLinks = [
    {
      id: "instagram",
      name: "Instagram",
      icon: <FaInstagram className="text-pink-500 text-xl" />,
      link: orgProfile?.[0]?.instagramLink,
    },
    {
      id: "twitter",
      name: "X",
      icon: <FaXTwitter className="text-black text-xl" />,
      link: orgProfile?.[0]?.twitterLink,
    },
    {
      id: "youtube",
      name: "YouTube",
      icon: <FaYoutube className="text-red-600 text-xl" />,
      link: "YouTube Link",
    },
  ];
  const businessTypesList = [
    { value: 1, label: "Sports Academy" },
    { value: 2, label: "Sports Club" },
    { value: 3, label: "Sports Training" },
    { value: 4, label: "Sports Facility" },
    { value: 5, label: "Sports League" },
    { value: 6, label: "Sports Fitness" },
    { value: 7, label: "Sports Health & Wellness" },
    { value: 8, label: "Sports Media & Broadcasting" },
    { value: 9, label: "Sports Marketing" },
    { value: 10, label: "Sports Equipment" },
    { value: 11, label: "Sports Education" },
    { value: 12, label: "Sports Merchandise" },
    { value: 13, label: "Sports Events Management" },
    { value: 14, label: "Sports Associations" },
    { value: 15, label: "Non-Profit Organizations" },
    { value: 16, label: "Sports Sponsorship" },
    { value: 17, label: "Educational Institution" },
  ];

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (resendCooldown > 0) {
      timer = setTimeout(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCooldown]);

  useEffect(() => {
    dispatch(fetchOrgProfile());
  }, []);

  useEffect(() => {
    dispatch(fetchAllStates());
  }, [dispatch]);

  useEffect(() => {
    selectedState?.value && dispatch(fetchAllLocations(selectedState?.value));
  }, [selectedState?.value, dispatch]);

  useEffect(() => {
    dispatch(fetchAllSports());
  }, [dispatch]);

  useEffect(() => {
    if (selectedSport?.value) {
      dispatch(fetchAllSpecialities(selectedSport.value));
    }
  }, [dispatch, selectedSport?.value]);

  useEffect(() => {
    dispatch(fetchAllAnnouncements());
  }, [dispatch]);

  console.log(
    "allAnnouncementsallAnnouncements",
    orgProfile?.[0]?.businessType?.businessName
  );

  return (
    <ClientGuard allowedRoles={[4]}>
      <div className="w-full flex justify-center items-center flex-wrap text-sm md:text-base font-medium mt-4 px-4">
        <span className="text-black">www.connectathlete/</span>
        <span className="text-black">
          {orgProfile?.[0]?.businessType?.businessName}/
        </span>
        <span className="text-black">{orgProfile?.[0]?.user?.id + "-"}</span>
        <input
          type="text"
          value={businessName}
          onChange={(e) => setBusinessName(e.target.value)}
          placeholder="BusinessName"
          className="border-none focus:outline-none text-green-500 bg-transparent font-semibold w-[160px] px-1 pt-1"
        />
      </div>
      <div className="max-w-screen-xl mx-auto w-full p-6 space-y-8">
        {/* Top Section: Profile + Announcement */}
        <div className="flex flex-col md:flex-row gap-6 w-full">
          {/* Left: Profile */}
          <div className="flex flex-col flex-[2] gap-6 bg-white p-6 rounded-xl shadow-md border border-gray-200">
            <div className="flex gap-4 items-start">
              <div className="w-24 h-24 rounded-full border overflow-hidden bg-black flex items-center justify-center">
                {orgProfile?.[0]?.user?.profileImg ? (
                  <Image
                    src={orgProfile[0].user.profileImg}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-12 h-12 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0zm6 1a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                )}
              </div>

              <div className="flex-1">
                <p className="text-blue-700 font-bold mb-1">Premium</p>
                <div className="flex items-center gap-2 mb-2">
                  {editingName ? (
                    <input
                      className="text-2xl font-bold border-b border-gray-300 focus:outline-none w-full"
                      value={orgProfile?.[0]?.organizationName}
                      onChange={(e) => setOrgName(e.target.value)}
                      onBlur={() => setEditingName(false)}
                      autoFocus
                    />
                  ) : (
                    <h2
                      className="text-2xl font-bold cursor-pointer"
                      onClick={() => setEditingName(true)}
                    >
                      {orgProfile?.[0]?.organizationName}
                    </h2>
                  )}
                  <PencilLine className="w-4 h-4 text-gray-500 cursor-pointer" />
                </div>

                <textarea
                  className="text-sm p-2 border rounded w-full font-medium resize-none"
                  value={blurb}
                  onChange={(e) => setBlurb(e.target.value)}
                  placeholder="Blurb"
                  rows={4}
                />
              </div>
            </div>

            {/* Dropdowns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CustomDropdown
                options={businessTypesList.map((item: any) => item.label)}
                selectedValue={orgProfile?.[0]?.businessType?.businessName}
                handleChange={() => { }}
                placeholder="Select a Business Type"
              />
              <CustomDropdown
                options={serviceChannelOptions.map((item: any) => item.label)}
                selectedValue={serviceChannel || ""}
                handleChange={handleChange}
                placeholder="Please Select Service Channel"
              />
            </div>

            {/* Contact Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                className="p-2 border rounded w-full"
                value={orgProfile?.[0]?.user?.firstName}
                onChange={(e) =>
                  setContact({ ...contact, firstName: e.target.value })
                }
                placeholder="First Name"
              />

              <div className="w-full">
                <div className="flex items-center gap-2 border rounded p-2 w-full">
                  <Mail className="w-4 h-4 text-gray-600" />
                  <input
                    className="w-full focus:outline-none"
                    value={orgProfile?.[0]?.user?.email}
                    onChange={(e) =>
                      setContact({ ...contact, email: e.target.value })
                    }
                    placeholder="Email"
                  />
                </div>

                {!isEmailVerified && (
                  <div className="mt-2 ml-1 text-sm text-red-600 flex flex-col gap-2">
                    <span>Your email is not verified.</span>
                    <button
                      onClick={handleResendVerification}
                      disabled={resendCooldown > 0}
                      className={`px-3 py-1 rounded-md text-white text-xs w-fit ${resendCooldown > 0
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700"
                        }`}
                    >
                      {resendCooldown > 0
                        ? `Resend in ${resendCooldown}s`
                        : "Verification Email"}
                    </button>
                    {resendMessage && (
                      <span className="text-green-600">{resendMessage}</span>
                    )}
                  </div>
                )}
              </div>

              <input
                className="p-2 border rounded w-full"
                value={orgProfile?.[0]?.user?.lastName}
                onChange={(e) =>
                  setContact({ ...contact, lastName: e.target.value })
                }
                placeholder="Last Name"
              />
              <div className="flex items-center gap-2 border rounded p-2 w-full">
                <Phone className="w-4 h-4 text-gray-600" />
                <input
                  className="w-full focus:outline-none"
                  value={orgProfile?.[0]?.phone}
                  onChange={(e) =>
                    setContact({ ...contact, phone: e.target.value })
                  }
                  placeholder="Phone"
                />
              </div>
              <input
                className="p-2 border rounded w-full"
                value={orgProfile?.[0]?.title}
                onChange={(e) =>
                  setContact({ ...contact, title: e.target.value })
                }
                placeholder="Title"
              />
              <div className="flex items-center gap-2 border rounded p-2 w-full">
                <Globe className="w-4 h-4 text-gray-600" />
                <input
                  className="w-full focus:outline-none"
                  value={orgProfile?.[0]?.websiteLink}
                  onChange={(e) =>
                    setContact({ ...contact, link: e.target.value })
                  }
                  placeholder="Website Link"
                />
              </div>
            </div>
            <div className="flex items-center gap-2 border rounded p-2 w-full">
              {/* <Globe className="w-4 h-4 text-gray-600" /> */}
              <label className="w-[280px]">Last Updated Date</label>
              <input
                className="w-full focus:outline-none"
                value={formatToDayMonthYear(
                  orgProfile?.[0]?.user?.lastTermsAcceptedAt
                )}
                placeholder="Last Updated Date"
              />
            </div>

            {/* About Section */}
            <div>
              <h3 className="text-lg font-semibold mb-2">
                {orgProfile?.[0]?.organizationName}
              </h3>

              <textarea
                className="p-2 border rounded w-full text-sm font-medium resize-none"
                value={about}
                onChange={(e) => setAbout(e.target.value)}
                placeholder="About your organization..."
                rows={4}
              />
            </div>
          </div>

          {/* Right: Top Announcements */}
          <div className="flex-[1] bg-white p-6 rounded-xl shadow-md border border-gray-200 h-fit min-h-[250px]">
            <h4 className="text-blue-600 font-bold mb-4 text-lg">
              Top Announcements
            </h4>
            <div className="bg-gray-300 text-black p-2 rounded text-sm font-medium">
              {/* <SpotLights />  */}
              <SpotLights data={allAnnouncements} />
            </div>
          </div>
        </div>

        <div className="max-w-screen-xl mx-auto w-full p-6 space-y-8">
          {/* Bottom Section: Upload to Gallery */}
          <h4 className="text-blue-600 font-bold mb-0 text-lg mt-4">
            Videos & Gallery
          </h4>
          <div className="flex flex-col md:flex-row gap-0 w-full">
            {/* Social Media Links */}
            <div className="flex space-y-0">
              {socialMediaLinks.map((media) => (
                <div
                  key={media.id}
                  className="cursor-pointer flex flex-col sm:flex-row items-center justify-between bg-white border border-gray-200 px-4 rounded-lg shadow-sm hover:shadow-md transition"
                >
                  <div className="flex flex-row items-center space-x-4 w-full sm:w-3/4">
                    <div className="w-10 h-10 flex items-center justify-center rounded-full bg-white border">
                      {media.icon}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Upload Section */}
            <div className="mt-10 flex-[2] bg-white border border-gray-200 rounded-xl shadow-md p-6 self-start min-h-[250px]">
              <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center gap-2">
                📁 Upload to Gallery
              </h2>
              <div className="flex flex-col sm:flex-row items-center justify-between bg-white border border-dashed border-gray-400 p-4 rounded-md hover:shadow-md transition">
                <div className="flex items-center space-x-4 w-full sm:w-2/3">
                  <p className="text-gray-700 text-sm">
                    Select an image to add to your gallery
                  </p>
                </div>
                <div className="mt-4 sm:mt-0 sm:w-1/3 text-right">
                  <label className="cursor-pointer inline-flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-400 hover:from-gray-700 hover:to-gray-500 text-white p-3 rounded-full transition duration-300 w-12 h-12">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16 16l-4-4-4 4m4-4v9m5-13a5 5 0 00-9.9-1.001A4.5 4.5 0 005 13.5h1"
                      />
                    </svg>
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                  </label>
                </div>
              </div>

              {selectedFile && previewUrl && (
                <div className="mt-4 p-4 border rounded-md bg-gray-50 text-sm text-gray-700">
                  <p className="mb-2 font-medium">
                    Selected File: {selectedFile.name}
                  </p>
                  <Image
                    src={previewUrl}
                    alt="Selected"
                    width={200}
                    height={150}
                    className="rounded-md object-cover"
                  />
                </div>
              )}
            </div>
          </div>

          {/* ... Your remaining dropdowns section (unchanged) */}
        </div>

        <div className="flex flex-col gap-5">
          <div className="grid grid-cols-1 xl:grid-cols-2 w-full items-center gap-3">
            <Select
              name="selectedState"
              options={allStatesList}
              value={selectedState}
              onChange={(selectedOption) => {
              }}
              isClearable
              placeholder="Select State..."
              className="w-full border-slate-300"
            />
            <Select
              name="selectedLocations"
              placeholder="Select Cities..."
              options={allLocationsList}
              isMulti
              value={selectedLocations}
              onChange={(selectedOption) => {
                let selected =
                  (selectedOption as {
                    value: number;
                    label: string;
                  }[]) || [];

                if (selected.some((city) => city.value === 0)) {
                  selected = [{ value: 0, label: "All Cities" }];
                } else {
                  selected = selected.filter((city) => city.value !== 0);
                }

              }}
            />
          </div>
        </div>
        <div className="flex flex-col gap-5">
          <div className="grid grid-cols-1 xl:grid-cols-2 w-full items-center gap-3">
            <Select
              name="selectedSport"
              options={allSportsList}
              value={selectedSport}
              onChange={(selectedOption) => {

              }}
              isClearable
              placeholder="Select Sports..."
              className="w-full border-slate-300"
            />
            <Select
              name="selectedSpecialities"
              placeholder="Select Specialities..."
              options={allSpecilitiesList}
              isMulti
              value={selectedSpecialities}
              onChange={(selectedOption) => {
                let selected =
                  (selectedOption as unknown as {
                    value: number;
                    label: string;
                  }[]) || [];

                if (selected.some((item) => item.value === 0)) {
                  selected = [{ value: 0, label: "All Specialities" }];
                } else {
                  selected = selected.filter((item) => item.value !== 0);
                }
              }}
            />
          </div>
        </div>
      </div>
    </ClientGuard>
  );
};

export default BusinessProfile;
